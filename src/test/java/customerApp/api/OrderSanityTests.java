package customerApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.module.jsv.JsonSchemaValidator;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.time.Duration;
import java.util.Collections;
import java.util.Random;
import java.util.stream.Collectors;

public class OrderSanityTests extends BaseTest {

    @Test(groups = {"order-migration","order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithDueAmountAndCollectedAmountEqualOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to have due amount of -1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder(),false ,defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),1.0);

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .getFirst()
                        .getStatus(),
                "completed",
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );
        
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert balance = 0 and due amount has been paid
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0 , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"create-order","order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCouponFreeDeliveryAndCollectedAmountGreaterThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Free delivery coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free delivery",
                "commercial",
                0,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder(),true
                , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock() ,0);
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Complete order cycle through the apis , setting collected amount to value > order total by 20
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() + 20
                , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getTestOrder().getDeliveryFees(), 0
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = the value added on order amount which is 20
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                20
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCouponFreeDeliveryAndCollectedAmountLessThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create free delivery coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free delivery",
                "commercial",
                0,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0, 2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod", "Cash On Delivery"
                , 0, defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , true, "Now", "processing", true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true, defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true, defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Complete order cycle through the APIs , with collected amount less than order amount by 1
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 1
                , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getTestOrder().getDeliveryFees(), 0
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current User balance is null and it shouldn't be.");

        //Assert balance has due amount = -1
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                -1.0f
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke","stable-test"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCouponFreeGiftAndPartialPayFromWalletAndCollectedAmountEqualOrderAmount() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create free gift coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createDiscountProductOrFreeGiftCoupon(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free gift",
                "commercial",
                1,
                "active",
                true,
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order using partial balance for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId() ,true ,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,1,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true,defaultTestData.get().getTestCoupon());
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Complete order using Apis with collected amount = order total - balance(1)
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 1
                , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()) );
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Wait for 120 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be");

        //Assert user balance = 0 after order completion
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke" , "stable-test"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"),@Tag("database")})
    public void createOrderWithTippingDuringCheckoutAndCollectedAmountEqualOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,"Call me"));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false , false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);

        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "10" , "0");

        Assert.assertNotNull(defaultTestData.get()
                .getRandomTestUser().getTestOrder().getGratuityAmount()
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert Gratuity value is correct after order creation
        Assert.assertEquals(Integer.valueOf(defaultTestData.get()
                .getRandomTestUser().getTestOrder().getGratuityAmount()), 10
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        // call Get order API
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        // Assert user details (id, name, address details) in get order API
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getUser());

        // Assert order id in create order response = order id in get order API
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());
        // Assert order number in create order response = order number in get order API
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderNumber(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());

        // Assert order status, isCancellable, CurrentOrderStatus in get order API
        getOrderApiValidator.get().assertOrderStatus(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getStatus(),"processing");
        getOrderApiValidator.get().assertCancellable(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().isCancellable(),true);
        getOrderApiValidator.get().assertCurrentOrderStatus(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getCurrentStatus(),"orderPlaced");

        // Assert delivery note in get order API
        getOrderApiValidator.get().assertOnDeliveryNote(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getDeliveryNote(),"Call me");

        // Assert on payment method, payment title and balance used in get order API
        getOrderApiValidator.get().assertPaymentMethod(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getPaymentMethod(),"cod");
        getOrderApiValidator.get().assertPaymentTitle(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getPaymentTitle(),"Cash On Delivery");
        getOrderApiValidator.get().assertBalanceUsed(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getBalanceUsedInOrder(),0);

        //Assert on NOW,isTimeSlotShifted, scheduled_express, isScheduled is true in get order API
        getOrderApiValidator.get().assertNow(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().isNow(),true);
        getOrderApiValidator.get().assertScheduledExpress(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getIsScheduledExpress(),false);

        // Assert user details (id, name, address details) in get order API
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getUser());
        //Assert on Gratuity amount
        Assert.assertEquals(Integer.valueOf(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getGratuityAmount()), 10);
        // Assert on product list in get order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false , false);

        //Complete order cycle from the APIs where collected amount  = order total + gratuity
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity(), false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                defaultTestData.get().getRandomTestUser().getId()
                , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert user balance is 0
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0.0f
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithTippingDuringCheckoutAndFullyPayFromBalanceAndCancel() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 1000
        defaultTestData.get().getRandomTestUser().
                setCurrentBalance(switcherApiClient.get().updateUserBalanceByPhoneNumber("1000.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Assert user balance updated to 1000
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())
                , 1000.0f);

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "15"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false , false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Balance"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "15" , "15");

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Cancel order
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "cancelled",false);

        //Update order in test data with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is cancelled
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        Thread.sleep(Duration.ofMinutes(5));

        //Update user's balance in test data
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert user has been refunded order amount - gratuity
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                1000.0f
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithBackToWalletCouponAndTippingDuringCheckoutAndFullyPayFromBalance() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "commercial",
                1000,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 1000
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1000.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "15"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId() ,false , false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Balance"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "15" , "15");
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert Gratuity value is correct after order creation
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getGratuityAmount(), "15"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Complete Order Cycle
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0.00f,false);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        // wait until balance reflected
        Thread.sleep(Duration.ofMinutes(5));

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert user balance has the order total deducted + back to wallet coupon value(1000)
                Assert.assertEquals(String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", (1000.00f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()) + 1000.00f)
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndGratuityAfterCheckout() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductObjectInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts().getFirst()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId());
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

       //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Payment Transaction Status updated to SUCCESS
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        defaultTestData.get().setProductStockLog(controlRoomV2ApiClient.get().getProductLogStock(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst().getMysqlId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        Assert.assertEquals(defaultTestData.get().getProductStockLog().getDelta(), -1);

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total+gratuity(5) subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 5),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithTippingDuringCheckoutAndNotReceived() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order COD with gratuity 10 and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false,false,false, true,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "10" , "0");
        //Asserting Gift Receipt
        createOrderApiValidator.get().assertGiftReceiptKeyInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder() , true);

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert Gratuity Amount in order is 10
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getGratuityAmount(), "10"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Mark order as not received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is not received
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithDueAmountAndNotReceived() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance have due amount = -1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),1);

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Mark order as not received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order is marked as not received
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Wait for 60 secs until used balance gets refunded
        Thread.sleep(Duration.ofMinutes(5));

        //Update random test user balance with current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert balance still has the due amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())
                , -1.00f
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithPartiallyPaidFromBalanceAndNotReceived(){

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order CC , fully paid by wallet and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,1,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is not received
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Update test user balance with the current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert amount paid by wallet is 0 as it is not refunded automatically
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithTippingDuringCheckoutAndFailed() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order COD with gratuity =10 and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Assert Gratuity
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "10" , "0");

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert gratuity value is 10 after placing order
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getGratuityAmount(), "10"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Mark order as failed
        orderApiClient.get().markOrderAsFailed(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "failed");

        //Assert order does not show in user orders
        Assert.assertTrue(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()).isEmpty());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderCODBalancePositiveNotUsedCollectedAmountGreaterThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Complete order cycle through the apis , setting collected amount to value > order total by 1
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() + 1
                , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = existing balance (1) + additional collected value (1)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                2
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithDueAmountAndCollectedAmountGreaterThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to have due amount of -1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),1);

        //Complete order cycle through the apis , setting collected amount to value > order total by 1
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() + 1
                , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert current balance = 1
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 1
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderCODBalancePositiveNotUsedAndTippingDuringRatingAndCollectedAmountLessThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 20
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("20.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Complete order cycle through the apis , setting collected amount to value < order total by 1
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 1.0f
                , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance());

        //Assert current balance = original balance(20) - amount collected less(1) - gratuity (5)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                14
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithDueAmountAndCollectedAmountLessThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to -1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),1);

        //Complete order cycle through the apis , setting collected amount to value < order total by 1
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
        , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 1.0f
        , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user's balance is null and it shouldn't be.");

        //Assert current balance = - 1
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                -1
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithBackToWalletFixedWithTippingDuringRatingAndPartialPayFromWalletAndCollectedAmountGreaterThanOrderAmount() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "commercial",
                100,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,1,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Complete order cycle through the apis
        //Setting collected amount = order amount -1(balance pre-existing) + 6(extra collected amount, for gratuity)
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , (defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 1) + 6
                , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Create Gratuity during Rating order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Wait for 120 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                                defaultTestData.get().getRandomTestUser().getId()
                                , defaultTestData.get().getAdminUser())
                        .getCurrentBalance());

        //Assert current balance = 100(coupon value) + 6(extra collected amount) - 5(gratuity)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                101, String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithPartialPayFromWalletAndCollectedAmountLessThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,1,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Complete order cycle through the apis , setting collected amount to -2
        // -2 = -1 from balance, and -1 to be less than the total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 2,false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = -20
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                -1, String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithTippingDuringCheckoutAndFullyPayFromWalletAndMarkOrderAsFree() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Balance"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Complete order cycle through the apis , setting mark order as free
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0.0f,true);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 500
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithFreeDeliveryCouponAndCancel() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Free delivery coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free delivery",
                "commercial",
                0,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getTestOrder().getDeliveryFees(), 0
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Cancel order
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "cancelled",false);

        //Update order in test data with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is cancelled
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithDueAmountAndCancel() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-50.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,true);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),50);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Await Order turns into processing instead of pending before cancelling
        Thread.sleep(Duration.ofSeconds(30));

        //Cancel order
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "cancelled",false);

        //Update order in test data with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is cancelled
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Wait for 60 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be");

        //Assert current balance = -50
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                -50, String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithBackToWalletPercentageWithTippingDuringCheckoutAndFullyPayFromWalletAndCollectedAmountGreaterThanOrderAmount() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Back to wallet coupon with percentage value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                true,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 1000
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Balance"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1" , "1");
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Complete order cycle through the apis , setting collected amount to value > order total by 20
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,1.0f,false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Wait for 120 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be");

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()
                , String.format("Total with Gratuity is null for Order ID: %s"
                        , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        // Verify that the user's current balance is updated correctly after the Coupon is reflected in wallet
        Assert.assertEquals(
                // Expected balance after coupon reflects to the nearest two decimals
                String.format("%.2f", Double.parseDouble(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                // Calculated balance after coupon reflects to the nearest two decimals
                String.format("%.2f", (
                        // Initial balance
                        500.0
                                // Subtract total with gratuity
                                - defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()
                                // Add 1.0 LE (Collected Amount Added when completing the order)
                                + 1.0
                                // Add Coupon value (10% of the cart value)
                                + (defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                                - defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()
                                - defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFeesInFeesObject()) * 0.1)),
                // Error message with order ID
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromBalanceAndFreeDeliveryCouponAndTippingDuringCheckout() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Free delivery coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free delivery",
                "commercial",
                0,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1" , "0");
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getTestOrder().getDeliveryFees(), 0
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        Thread.sleep(Duration.ofSeconds(30));

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Order Transaction updated to SUCCESS
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be");

        //Assert User Balance has order amount deducted , coupon & collected amount Reflected
        Assert.assertEquals(String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()))
                , String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity())
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromBalanceWithTippingDuringCheckoutThenMarkOrderAsNotReceived() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to have amount of 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct(),0);
        //Assert gratuity
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1","0" );

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Await order becomes processing first
        Thread.sleep(Duration.ofSeconds(60));

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order is marked as not received
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");

        //Update random test user balance with current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert balance still has the same amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 500
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCAndTippingFullyPaidFromBalanceAndFreeGiftCouponAndMarkOrderAsCancelled() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create free gift coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createDiscountProductOrFreeGiftCoupon(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free gift",
                "commercial",
                1,
                "active",
                true,
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),true,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Assert Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(defaultTestData.get().getTestCoupon().getCouponCode()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "5" , "0");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Await order become processing before cancelling it
        Thread.sleep(Duration.ofSeconds(120));

        //Mark order as cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",false);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is cancelled
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        Thread.sleep(Duration.ofMinutes(5));

        //Update random test user balance with current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert balance still has the same amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderCODAndTippingDuringCheckoutAndMarkOrderAsCancelled() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Assert Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1","0");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Mark order as cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",false);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is cancelled
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletAndMarkOrderAsCancelled() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Await order become processing before cancelling it
        Thread.sleep(Duration.ofSeconds(120));

        //Mark order as cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",false);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is cancelled
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        Thread.sleep(Duration.ofMinutes(5));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance is the same before the order
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500 , String.format("Order ID: %s",
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletAndMarkOrderAsFree() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(),
                true, "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Assert Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "10","0");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),
                0.0f, true);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 500
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createForcedScheduleOrderWithCouponFreeDeliveryAndCollectedAmountGreaterThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        // Update Fb capacity to current timeslot
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                Collections.singletonList(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotId()),
                0);

        //Create Free delivery coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free delivery",
                "commercial",
                0,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
       //Validate Time Slots , delivery date & schedule keys in Create Order Response
        createOrderApiValidator.get().assertWarehouseTimeslotDeliveryDateAndScheduleKeysInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true ,false
                , testExecutionHelper.get().getFutureTimeStamp("hh:mm a",3,1)
                , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"));
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Complete order cycle through the apis , setting collected amount to value > order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() + 20,false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that Order is schedule
        Assert.assertTrue(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().isScheduled());

        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getTestOrder().getDeliveryFees(), 0
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = the value added on order amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 20
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createForcedScheduleOrderWithCCFullyPaidFromWalletAndBackToWalletPercentageCoupon() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        // Update Fp capacity to current timeslot
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                Collections.singletonList(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotId()),
                0);

        //Create Back to wallet coupon with percentage value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                true,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(),
                true, "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate Time Slots , delivery date & schedule keys in Create Order Response
        createOrderApiValidator.get().assertWarehouseTimeslotDeliveryDateAndScheduleKeysInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true ,false
                , testExecutionHelper.get().getFutureTimeStamp("hh:mm a",3,1)
                , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"));
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),
                1.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that Order is schedule
        Assert.assertTrue(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().isScheduled());

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Wait for coupon value to reflect in balance
        Thread.sleep(Duration.ofMinutes(5));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert User Balance has amount paid , coupon & collected amount Reflected
        // Cart Value = total-delivery fees - service fees
        //Coupon value = cart value rounded * percentage of discount;
        Assert.assertEquals(
                // Expected balance after coupon reflects to the nearest two decimals
                String.format("%.2f",Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                // Calculated balance after coupon reflects to the nearest two decimals
                String.format("%.2f", ((
                        // Initial balance
                        500 -
                                // Subtract total
                                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal())
                                // Add Coupon value (10% of the cart value)
                                + (defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                                - defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()
                                - defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFeesInFeesObject()) * 0.1f)
                        //Additional Collected amount added in order completion added
                        + 1)
                // Error message with order ID
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Cash On Delivery");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createGiftOrderWithTippingDuringCheckout() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,true,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gift Receipt
        createOrderApiValidator.get().assertGiftReceiptKeyInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder() , true);
        //Assert Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1","0");

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity(),false);

        // Assert that order is gift
        Assert.assertTrue(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().isGiftReceipt());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert balance = 0 and no due amount is left
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0 , String.format("Order ID: %s",
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithFixedCartCouponAndTippingDuringCheckout() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Fixed Cart coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "fixed_cart",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,true,""));

        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());
        //Assert Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1","0");
        //Asserting Gift Receipt
        createOrderApiValidator.get().assertGiftReceiptKeyInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder() , true);

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity(),false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert balance = 0 and no due amount is left
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0 , String.format("Order ID: %s",
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithPercentageCartCouponAndTippingDuringCheckout() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Percentage Cart coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "percent",
                "commercial",
                0,
                0,
                10,
                "active",
                true,
                true,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,true,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1" , "0");
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());
        //Asserting Gift Receipt
        createOrderApiValidator.get().assertGiftReceiptKeyInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder() , true);

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity(),false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert balance = 0 and no due amount is left
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0 , String.format("Order ID: %s",
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void validateCreateOrderWithSingleProductSchema() {
        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        orderApiClient.get().getCreateOrderEndpointResponse(
                 defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getAddress()
                ,"0"
                ,"10"
                ,"cod"
                ,defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                ,false,false, false, false).
                then().assertThat().
                body(JsonSchemaValidator.matchesJsonSchemaInClasspath("createOrderSchema.json"));
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void validateCreateOrderWithMultiProductsSchema() {
        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        orderApiClient.get().getCreateOrderWithMultipleProductsEndpointResponse(
                        defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getRandomTestUser().getAddress()
                        ,"0"
                        ,"10"
                        ,"cod"
                        ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        ,""
                        ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        ,testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false, false,"").
                then().assertThat().
                body(JsonSchemaValidator.matchesJsonSchemaInClasspath("createOrderWithMultiProductsSchema.json"));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createScheduledOrder() {
        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, true,false,false,""));

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate Time Slots , delivery date & schedule keys in Create Order Response
        createOrderApiValidator.get().assertWarehouseTimeslotDeliveryDateAndScheduleKeysInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true ,false
                , testExecutionHelper.get().getFutureTimeStamp("hh:mm a",3,1)
                , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"));
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Assert that Order is scheduled
        Assert.assertTrue(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().isScheduled());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createScheduledExpressOrder() {
        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        // Update Fb capacity to current timeslot
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                Collections.singletonList(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotId()),
                0);

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, true,true,false,""));

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate Time Slots , delivery date & schedule keys in Create Order Response
        createOrderApiValidator.get().assertWarehouseTimeslotDeliveryDateAndScheduleKeysInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true ,true
                , testExecutionHelper.get().getFutureTimeStamp("hh:mm a",3,1)
                , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"));
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Assert that Order is scheduled Express
        Assert.assertTrue(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getIsScheduledExpress());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void validateProductStockAfterCreateOrderWithDeliveryFeesAndTippingAmount() throws InterruptedException {
        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Await product stock reflection in logs
        Thread.sleep(Duration.ofMinutes(1));

        defaultTestData.get().setProductStockLog(controlRoomV2ApiClient.get().getProductLogStock(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        Assert.assertEquals(defaultTestData.get().getProductStockLog().getDelta(), -1);
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndPercentageCoupon() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Percentage Cart coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "percent",
                "commercial",
                0,
                0,
                10,
                "active",
                true,
                true,
                "now"));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts().subList(0,2)
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId())
                ,"SUCCESS");

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletThenVoidPayment() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Void Payment From Payment Panel
        paymentPanelApiClient.get().voidPayment(defaultTestData.get().getPaymentPanelUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());

        //Await payment to get voided
        Thread.sleep(Duration.ofMinutes(1));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order transaction status is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "VOIDED");
        //Assert Wallet transaction in payment Panel is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");
        //Assert wallet Transaction Status is voided
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId()),
                "VOIDED");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is Processing
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 500 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500);
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletTippingFromCheckoutThenVoidPayment() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "5" , "0");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Void Payment From Payment Panel
        paymentPanelApiClient.get().voidPayment(defaultTestData.get().getPaymentPanelUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());

        //Await payment tp get voided
        Thread.sleep(Duration.ofMinutes(1));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order transaction status is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "VOIDED");
        //Assert CC transaction in payment Panel is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is Processing
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 500 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500);

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletAndTippingDuringCheckoutAndPartialRefundOrderThroughSwitcherWithGratuityIssueReasonToCreditCard() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

//        //Update test order with the latest status
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
//
//        //Assert that order status has been changed to completed
//        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
//        Assert.assertEquals(defaultTestData.get()
//                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
//                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
//        Assert.assertFalse(defaultTestData.get()
//                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        // refund order with delivery issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                Float.valueOf(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()),
                false,
                "gratuity issue",
                0);

        Thread.sleep(Duration.ofSeconds(30));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        Thread.sleep(Duration.ofSeconds(30));

        //Assert Refund Type
        Assert.assertNotEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Credit Card");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()),
                0.01
        );
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletTippingFromCheckoutAndFullyRefundGratuityOrderThroughPaymentPanelToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getRefundWalletPaymentTransactionAmount());

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500 - (Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount())),
                0.01
        );
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndTippingDuringRatingAndFullyRefundOrderThroughPaymentPanelToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Payment Transaction Status updated to SUCCESS
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Thread.sleep(10000);

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getRefundWalletPaymentTransactionAmount());

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndTippingDuringRatingAndFullyRefundOrderWithSelectCCThroughPaymentPanelToWallet(){

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Payment Transaction Status updated to SUCCESS
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total+gratuity(5) subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 5),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order Amount
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getRefundWalletPaymentTransactionAmount());

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndTippingDuringRatingAndPartialRefundOrderThroughPaymentPanelToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Payment Transaction Status updated to SUCCESS
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total+gratuity(5) subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 5),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order Amount
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getRefundWalletPaymentTransactionAmount());

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndTippingDuringRatingAndFullyRefundOrderThroughSwitcherToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Payment Transaction Status updated to SUCCESS
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total+gratuity(5) subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 5),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        // refund order with order bad experience reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                true,
                "order bad experience",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                0.01
        );

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndPartialRefundOrderThroughSwitcherToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        // refund order with order bad experience reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                2,
                true,
                "order bad experience",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                2.00,
                0.01
        );
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web") ,@Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletAndTippingDuringCheckoutAndPartialRefundOrderThroughSwitcherWithGratuityIssueReasonToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        // refund order with gratuity issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                Float.valueOf(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()),
                true,
                "gratuity issue",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()),
                0.01
        );
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web") , @Tag("database")})
    public void createOrderWithCCrWithCCFullyPaidFromWalletAndTippingDuringRatingAndPartialRefundOrderThroughSwitcherWithDeliveryIssueReasonToCreditCard() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        Thread.sleep(Duration.ofMinutes(1));

        //Assert wallet Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId()),
                "SUCCESS");

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        // refund order with gratuity issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                13,
                true,
                "delivery issue",
                0);

        Thread.sleep(Duration.ofSeconds(30));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        Thread.sleep(Duration.ofSeconds(30));

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(), 0.01);
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletTippingFromCheckoutAndRefundOrderWithLateDeliveryIssueToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        // refund order with delivery issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(),
                false,
                "delivery issue",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        Thread.sleep(Duration.ofSeconds(60));

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(), 0.01);
    }

}
